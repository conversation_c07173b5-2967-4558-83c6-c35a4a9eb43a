services:
    postgresql:
        image: docker.io/bitnami/postgresql:17
        ports:
            - '5432:5432'
        environment:
            - POSTGRESQL_USERNAME=${DB_USERNAME}
            - POSTGRESQL_PASSWORD=${DB_PASSWORD}
            - POSTGRESQL_DATABASE=${DB_DATABASE}
        volumes:
            - 'postgresql_data:/bitnami/postgresql'
        container_name: ${APP_NAME}-postgresql
        networks:
            - app-network

    php-fpm:
        build:
            context: ./docker/php-fpm
            dockerfile: Dockerfile
        container_name: ${APP_NAME}-php-fpm
        volumes:
            - ./:/var/www/html
        networks:
            - app-network

    nginx:
        build:
            context: ./docker/nginx
            dockerfile: Dockerfile
        container_name: ${APP_NAME}-nginx
        ports:
            - 80:8080
        volumes:
            - ./:/var/www/html
        networks:
            - app-network

    php-cli:
        stdin_open: true
        tty: true
        build:
            context: ./docker/php-cli
            dockerfile: Dockerfile
        container_name: ${APP_NAME}-php-cli
        volumes:
            - ./:/var/www/html
        networks:
            - app-network

volumes:
    postgresql_data:
        driver: local

networks:
    app-network:
