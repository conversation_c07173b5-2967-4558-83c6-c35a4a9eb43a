#!/bin/bash
set -e

USER_ID=${UID:-1000}
GROUP_ID=${GID:-1000}

# Crear grupo si no existe
if ! getent group "$GROUP_ID" >/dev/null; then
    groupadd -g "$GROUP_ID" appgroup
fi

if ! getent passwd "$USER_ID" >/dev/null; then
    useradd -u "$USER_ID" -g "$GROUP_ID" -m -s /bin/bash appuser
fi

mkdir -p storage/logs
mkdir -p storage/framework/cache
mkdir -p storage/framework/sessions
mkdir -p storage/framework/views
mkdir -p bootstrap/cache

chown -R "$USER_ID":"$GROUP_ID" /var/www/html

chmod -R 775 storage/
chmod -R 775 bootstrap/cache/

touch ZCRMClientLibrary.log
chmod 666 ZCRMClientLibrary.log
chown "$USER_ID":"$GROUP_ID" ZCRMClientLibrary.log

exec gosu "$USER_ID":"$GROUP_ID" "$@"
